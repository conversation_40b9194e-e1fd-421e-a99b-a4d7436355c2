import json

import requests
import re
import base64
from PIL import Image
import os
import io
import ddddocr
import time # 用于生成唯一文件名
from datetime import datetime # 用于生成唯一文件名
from typing import  Any, Dict, List, Optional
from lxml import etree, html
import requests






class AppleWarrantyExtractorFromHtml:
    def __init__(self):
        self.data = {}

    def extract_warranty_info(self, html_content):
        """
        从HTML内容中提取苹果保修信息
        """
        try:
            # 解析HTML
            tree = html.fromstring(html_content)

            # 提取设备信息
            device_info = self._extract_device_info(tree)

            # 提取保修信息
            warranty_info = self._extract_warranty_info(tree)

            # 提取支持链接
            support_links = self._extract_support_links(tree)

            # 整合所有信息
            result = {
                'device_info': device_info,
                'warranty_info': warranty_info,
                'support_links': support_links,
                'extraction_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            return result

        except Exception as e:
            print(f"提取信息时发生错误: {e}")
            return None

    def _extract_device_info(self, tree):
        """提取设备基本信息"""
        device_info = {}

        # 设备型号
        device_title = tree.xpath('//h1[@class="device-header-title"]/text()')
        device_info['device_model'] = device_title[0].strip() if device_title else "未找到"

        # 序列号
        serial_number = tree.xpath('//p[@class="device-header-serial-number"]/text()')
        if serial_number:
            device_info['serial_number'] = serial_number[0].replace('序列号：', '').strip()
        else:
            device_info['serial_number'] = "未找到"

        # 购买日期
        purchase_date = tree.xpath('//p[@class="device-header-purchase"]/text()')
        if purchase_date:
            device_info['purchase_date'] = purchase_date[0].replace('购买日期：', '').strip()
        else:
            device_info['purchase_date'] = "未找到"

        # 设备图片URL
        device_image = tree.xpath('//img[@class="device-header-image"]/@src')
        device_info['device_image_url'] = device_image[0] if device_image else "未找到"

        return device_info

    def _extract_warranty_info(self, tree):
        """提取保修信息"""
        warranty_info = {}

        # 保修类型
        warranty_type = tree.xpath('//h3[@class="header-title"]/text()')
        warranty_info['warranty_type'] = warranty_type[0].strip() if warranty_type else "未找到"

        # 保修到期日期
        expiry_date = tree.xpath('//div[@class="header-sub-title"]/span/text()')
        if expiry_date:
            warranty_info['expiry_date'] = expiry_date[0].replace('到期日期：', '').strip()
        else:
            warranty_info['expiry_date'] = "未找到"

        # 保障范围
        benefits = tree.xpath('//span[@class="benefits-description "]/text()')
        warranty_info['benefits'] = [benefit.strip() for benefit in benefits] if benefits else []

        # 保修图标URL
        warranty_icon = tree.xpath('//img[@class="card-image-wrapper"]/@src')
        warranty_info['warranty_icon_url'] = warranty_icon[0] if warranty_icon else "未找到"

        return warranty_info

    def _extract_support_links(self, tree):
        """提取支持链接"""
        support_links = {}

        # 请求维修链接
        repair_link = tree.xpath('//a[contains(text(), "请求维修")]/@href')
        support_links['repair_link'] = repair_link[0] if repair_link else "未找到"

        # 获取支持链接
        support_link = tree.xpath('//a[contains(text(), "获取支持")]/@href')
        support_links['support_link'] = support_link[0] if support_link else "未找到"

        # Apple保障范围信息链接
        warranty_info_link = tree.xpath('//a[contains(text(), "了解你产品的 Apple 保障范围信息")]/@href')
        support_links['warranty_info_link'] = warranty_info_link[0] if warranty_info_link else "未找到"

        # 法律详情链接
        legal_link = tree.xpath('//a[contains(text(), "查看详细信息")]/@href')
        support_links['legal_link'] = legal_link[0] if legal_link else "未找到"

        return support_links

    def print_formatted_result(self, result):
        """格式化打印结果"""
        if not result:
            print("未能提取到信息")
            return

        print("=" * 60)
        print("苹果设备保修信息提取结果")
        print("=" * 60)
        print(f"提取时间: {result['extraction_time']}")
        print()

        # 设备信息
        print("【设备信息】")
        print(f"设备型号: {result['device_info']['device_model']}")
        print(f"序列号: {result['device_info']['serial_number']}")
        print(f"购买日期: {result['device_info']['purchase_date']}")
        print(f"设备图片: {result['device_info']['device_image_url']}")
        print()

        # 保修信息
        print("【保修信息】")
        print(f"保修类型: {result['warranty_info']['warranty_type']}")
        print(f"到期日期: {result['warranty_info']['expiry_date']}")
        print(f"保障范围: {', '.join(result['warranty_info']['benefits'])}")
        print(f"保修图标: {result['warranty_info']['warranty_icon_url']}")
        print()

        # 支持链接
        print("【支持链接】")
        print(f"请求维修: {result['support_links']['repair_link']}")
        print(f"获取支持: {result['support_links']['support_link']}")
        print(f"保障范围信息: {result['support_links']['warranty_info_link']}")
        print(f"法律详情: {result['support_links']['legal_link']}")
        print("=" * 60)



class AppleCoverageChecker:
    BASE_URL = "https://checkcoverage.apple.com"
    CAPTCHA_IMAGE_DIR = "captcha_images" # 保存验证码图片的文件夹

    def __init__(self, serial_number: str):
        self.serial_number = serial_number
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            "Host": "checkcoverage.apple.com"
        })
        self.jwt_token = None
        os.makedirs(self.CAPTCHA_IMAGE_DIR, exist_ok=True) # 创建保存验证码的文件夹
        

    def _get_common_api_headers(self):
        if not self.jwt_token:
            raise ValueError("JWT token is not set. Call get_jwt_token first.")
        return {
            'Origin': self.BASE_URL,
            'Referer': f'{self.BASE_URL}/',
            'accept': 'application/json',
            # 'x-apple-auth-token': self.jwt_token,
            'x-apple-csrf-token': self.jwt_token,
        }
        

    def get_jwt_token(self):
        """步骤1: 获取JWT Token"""
        print("[步骤1] 正在获取JWT Token...")
        try:
            resp = self.session.get(
                f"{self.BASE_URL}/?locale=zh_CN",
                timeout=10
            )
            resp.raise_for_status() # 检查请求是否成功
            html_content = resp.text
            jwt_pattern = r'<meta name="csrf-token" content="([^"]+)"'
            jwt_match = re.search(jwt_pattern, html_content)
            if jwt_match:
                self.jwt_token = jwt_match.group(1)
                print(f"[步骤1] JWT Token获取成功: ...{self.jwt_token[-20:]}") # 打印部分token
                return True
            else:
                print("[步骤1] 错误: 未能在HTML中找到JWT Token。")
                return False
        except requests.RequestException as e:
            print(f"[步骤1] 错误: 获取JWT Token时发生网络错误: {e}")
            return False
            
            
    def _get_page_init(self):
        json_data = {}
        
        response = self.session.post(
            'https://checkcoverage.apple.com/api/v1/facade/page-init',
            headers=self._get_common_api_headers(),
            json=json_data,
        )
        response.raise_for_status()
        
        

    def submit_user_consent(self):
        """步骤2: 提交用户同意"""
        if not self.jwt_token:
            print("[步骤2] 错误: JWT Token未设置，无法提交用户同意。")
            return False

        headers = self._get_common_api_headers()

        first_url = "https://checkcoverage.apple.com/api/v1/consent?locale=zh_CN"
        first_resp = self.session.post(first_url, headers=headers)
        first_resp_data = first_resp.json()
        print(first_resp_data)
        if first_resp_data['data']['userConsented'] is True and first_resp_data['meta']['status'] == "SUCCESS":
            print("第一次处理成功")
        try:
            second_url = "https://checkcoverage.apple.com/?locale=zh_CN&_rsc=x5c21"
            second_resp = self.session.get(second_url, headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "Referer": "https://checkcoverage.apple.com/",
            })

            second_resp.raise_for_status()

            print(f"[步骤2] 用户同意提交成功, 状态码: {second_resp.status_code}")
            return True
        except requests.RequestException as e:
            print(f"[步骤2] 错误: 提交用户同意时发生网络错误: {e}, 响应: {e.response.text if e.response else 'N/A'}")
            return False


        # print("[步骤2] 正在提交用户同意...")
        # headers['content-type'] = 'application/json'
        #
        # consent_data = {"userConsent": True}
        # try:
        #     resp = self.session.post(
        #         f'{self.BASE_URL}/api/v1/facade/user-consent',
        #         headers=headers,
        #         json=consent_data,
        #         timeout=10
        #     )
        #     resp.raise_for_status()
        #     print(f"[步骤2] 用户同意提交成功, 状态码: {resp.status_code}")
        #     return True
        # except requests.RequestException as e:
        #     print(f"[步骤2] 错误: 提交用户同意时发生网络错误: {e}, 响应: {e.response.text if e.response else 'N/A'}")
        #     return False

    def get_captcha_data(self):
        """步骤3: 获取验证码数据 (Base64)"""
        if not self.jwt_token:
            print("[步骤3] 错误: JWT Token未设置，无法获取验证码。")
            return None

        print("[步骤3] 正在获取验证码数据...")
        headers = self._get_common_api_headers()
        params = {'type': 'image', 'locale': 'zh_CN'}
        
        new_headers = headers.copy()
        
        # new_headers["Referer"] = "https://checkcoverage.apple.com/api/v1/facade/user-consent"
        new_headers["Referer"] = "https://checkcoverage.apple.com/"

        # print("new_headers:", new_headers)
        # print("headers:", headers)
        
        try:
            resp = self.session.get(
                f'{self.BASE_URL}/api/v1/captcha',
                params=params,
                # headers=headers,
                headers=new_headers,
                timeout=10,
                allow_redirects=True
            )
            # if resp.headers.get("Location") and ("user-consent" in resp.headers.get("Location")):
                # if not self.submit_user_consent():
                    # return {"error": "Failed to submit user consent."}
                # resp = self.session.get(
                # f'{self.BASE_URL}/api/v1/facade/captcha',
                # params=params,
                # headers=headers,
                # timeout=10,
                # allow_redirects=False
            # )
            print(resp.headers)
            print(resp.text)
            resp.raise_for_status()
            resp_data = resp.json()
            base64_captcha = resp_data.get("data").get('binary')
            if base64_captcha:
                print(f"[步骤3] 验证码数据获取成功。")
                return base64_captcha
            else:
                print("[步骤3] 错误: 未在响应中找到 'binaryValue' (验证码数据)。")
                return None
        except requests.RequestException as e:
            print(f"[步骤3] 错误: 获取验证码数据时发生网络错误: {e}, 响应: {e.response.text if e.response else 'N/A'}")
            return None
        except requests.exceptions.JSONDecodeError:
            print(f"[步骤3] 错误: 解析验证码响应JSON失败。响应内容: {resp.text}")
            return None

    def save_and_recognize_captcha(self, base64_data: str):
        """步骤4: 保存验证码图片到本地并进行识别"""
        if not base64_data:
            return None
            
        try:
            img_data = base64.b64decode(base64_data)
        except base64.binascii.Error as e:
            print(f"[步骤4] 错误: Base64解码失败: {e}")
            return None

        # 保存图片
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        image_filename = f"captcha_{self.serial_number}_{timestamp}.png"
        image_path = os.path.join(self.CAPTCHA_IMAGE_DIR, image_filename)
        
        try:
            img = Image.open(io.BytesIO(img_data))
            if img.mode != 'RGB':
                img = img.convert('RGB')
            img.save(image_path, format='PNG')
            print(f"[步骤4] 验证码图片已保存到: {image_path}")
        except Exception as e:
            print(f"[步骤4] 错误: 保存验证码图片失败: {e}")
            return None # 如果保存失败，也无法识别

        # 识别验证码 (使用原始解码的 img_data 进行识别，避免重复读取文件)
        # ddddocr 需要的是图片文件的字节流
        try:
            img_bytes_for_ocr = io.BytesIO()
            img.save(img_bytes_for_ocr, format='PNG') # 重新保存到BytesIO对象，以获取ddddocr需要的格式
            img_bytes_for_ocr = img_bytes_for_ocr.getvalue()
            
            ocr = ddddocr.DdddOcr(det=False, ocr=True, show_ad=False)
            captcha_text = ocr.classification(img_bytes_for_ocr)

            if isinstance(captcha_text, str):
                captcha_text = re.sub(r'[^A-Za-z0-9]', '', captcha_text).upper() # 通常验证码不区分大小写，转大写
                if captcha_text and len(captcha_text) >= 3: # 苹果验证码通常是5-6位
                    print(f"[步骤4] 验证码识别成功: {captcha_text}")
                    return captcha_text
                else:
                    print(f"[步骤4] 验证码识别结果不符合要求 (长度/内容): '{captcha_text}'")
                    return None
            else:
                print(f"[步骤4] 验证码识别返回非字符串类型: {type(captcha_text)}")
                return None
        except Exception as e:
            print(f"[步骤4] 错误: 验证码识别过程中发生错误: {e}")
            return None


    def submit_coverage_data(self, captcha_answer: str):
        """步骤5: 提交序列号和验证码答案"""
        if not self.jwt_token:
            print("[步骤5] 错误: JWT Token未设置，无法提交数据。")
            return None
        if not captcha_answer:
            print("[步骤5] 错误: 验证码答案为空，无法提交数据。")
            return None

        print(f"[步骤5] 正在提交序列号 '{self.serial_number}' 和验证码 '{captcha_answer}'...")
        headers = self._get_common_api_headers()
        headers['content-type'] = 'application/json'
        
        payload = {
            'serialInput': self.serial_number,
            'answer': captcha_answer,
            'captchaType': 'image',
        }
        try:
            resp = self.session.post(
                # f'{self.BASE_URL}/api/v1/facade/coverage',
                f'{self.BASE_URL}/api/v1/captchaValidate',
                headers=headers,
                json=payload,
                timeout=15
            )
            resp.raise_for_status()
            final_data = resp.json()
            print(f"[步骤5] 数据提交成功。")
            return final_data
        except requests.RequestException as e:
            error_msg = f"[步骤5] 错误: 提交数据时发生网络错误: {e}"
            if e.response is not None:
                error_msg += f", 响应: {e.response.text}"
                try:
                    return e.response.json() # 尝试返回错误JSON，以便上层判断
                except requests.exceptions.JSONDecodeError:
                    pass # 如果不是json，就返回None
            print(error_msg)
            return None
        except requests.exceptions.JSONDecodeError:
            print(f"[步骤5] 错误: 解析最终响应JSON失败。响应内容: {resp.text}")
            return None
            
    def check_coverage(self, max_retries=3):
        """执行完整的保修检查流程"""
        if not self.get_jwt_token():
            return {"error": "Failed to get JWT token."}
            
        # self._get_page_init()

        if not self.submit_user_consent():
            return {"error": "Failed to submit user consent."}

        for attempt in range(max_retries):
            print(f"\n--- 第 {attempt + 1}/{max_retries} 次尝试获取和识别验证码 ---")
            
            base64_captcha = self.get_captcha_data()
            if not base64_captcha:
                if attempt < max_retries - 1:
                    print("获取验证码失败，稍后重试...")
                    time.sleep(2)
                    continue
                else:
                    return {"error": "Failed to get captcha data after multiple retries."}

            captcha_code = self.save_and_recognize_captcha(base64_captcha)
            if not captcha_code:
                if attempt < max_retries - 1:
                    print("验证码识别失败，获取新验证码重试...")
                    time.sleep(2) # 等待一下再获取新的验证码
                    continue
                else:
                    return {"error": "Failed to recognize captcha after multiple retries."}
            
            final_response = self.submit_coverage_data(captcha_code)

            if final_response:
                print("[步骤6] 最终响应数据:")
                # print(final_response) # 打印完整响应以供调试
                
                # 检查是否是验证码错误
                error_message = final_response.get('error', '')
                if final_response.get('meta').get('status') == "FAILURE" and ("输入的代码与此图片不匹配" in error_message.get("message") or "CAPTCHA_IMAGE_NOT_MATCH" in error_message.get("token")):
                    print(f"[步骤6] 验证码错误: {error_message}")
                    if attempt < max_retries - 1:
                        print("将获取新的验证码并重试...")
                        time.sleep(2) # 等待一下再获取新的验证码
                        continue # 继续循环，获取新验证码
                    else:
                        print("已达到最大重试次数，验证码依然错误。")
                        return {"error": "Captcha incorrect after multiple retries.", "details": final_response}
                # elif final_response.get('errorType') is not None and final_response.get('errorType') != 0 : # 其他类型的错误
                #     print(f"[步骤6] 查询时遇到错误: {error_message or final_response}")
                #     return {"error": "API returned an error during coverage check.", "details": final_response}
                else: # 成功获取到数据
                    print("[步骤6] 查询成功！")

                    fin_resp = self.session.get("https://checkcoverage.apple.com/coverage?locale=zh_CN&_rsc=1n8m2",headers={
                            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                            "Referer": "https://checkcoverage.apple.com/",
                            "Host": "checkcoverage.apple.com/",
                        })

                    fin_resp.encoding = "utf-8"

                    fin_resp = fin_resp.text
                    # 从scripts中提取json结构，目前存在问题
                    # re_compiled = re.compile(r'<script>self.__next_f.push\((.*?)\)</script>', re.DOTALL)
                    # 直接提取html结构
                    res = AppleWarrantyExtractorFromHtml().extract_warranty_info(fin_resp)
                    return res


            else: # submit_coverage_data 返回 None，通常是网络错误或JSON解析错误
                if attempt < max_retries - 1:
                    print("提交数据失败或响应无效，稍后重试...")
                    time.sleep(3)
                    continue
                else:
                    return {"error": "Failed to submit coverage data or received invalid response after multiple retries."}
        
        return {"error": "Max retries reached for captcha validation."}


def main():
    """主函数，执行保修查询流程"""
    print("--- 苹果产品保修状态查询工具 ---")
    # LF6NQXV76V
    serial_to_check = input("请输入苹果设备序列号: ").strip().upper()

    if not serial_to_check:
        print("错误: 序列号不能为空。")
        return
    
    print(f"\n正在为序列号: {serial_to_check} 查询保修状态，请稍候...")
    checker = AppleCoverageChecker(serial_number=serial_to_check)
    response_data = checker.check_coverage(max_retries=3)

    print("\n--- 查询结果 ---")
    if response_data:
        print(json.dumps(response_data, ensure_ascii=False, indent=4))
    else:
        print("未能获取到任何查询结果。")
    print("-----------------")


if __name__ == "__main__":
    main()
